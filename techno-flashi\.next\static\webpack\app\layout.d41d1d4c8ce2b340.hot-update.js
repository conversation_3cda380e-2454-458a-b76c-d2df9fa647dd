"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"3c89128ceeca\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGlzbWFpbFxcRGVza3RvcFxcOTk5OTk5OVxcdGVjaG5vLWZsYXNoaVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiM2M4OTEyOGNlZWNhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ads/TechnoFlashBanner.tsx":
/*!**************************************************!*\
  !*** ./src/components/ads/TechnoFlashBanner.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TechnoFlashContentBanner: () => (/* binding */ TechnoFlashContentBanner),\n/* harmony export */   TechnoFlashFooterBanner: () => (/* binding */ TechnoFlashFooterBanner),\n/* harmony export */   TechnoFlashHeaderBanner: () => (/* binding */ TechnoFlashHeaderBanner),\n/* harmony export */   \"default\": () => (/* binding */ TechnoFlashBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _components_HydrationSafeWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/HydrationSafeWrapper */ \"(app-pages-browser)/./src/components/HydrationSafeWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,TechnoFlashHeaderBanner,TechnoFlashFooterBanner,TechnoFlashContentBanner auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n/**\n * مكون الإعلان المتحرك الحصري من تكنوفلاش\n * يظهر في جميع أنحاء الموقع مع تأثيرات متحركة\n */ function TechnoFlashBanner(param) {\n    let { position, className = '' } = param;\n    _s();\n    const [bannerData, setBannerData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"TechnoFlashBanner.useEffect\": ()=>{\n            fetchBanner();\n        }\n    }[\"TechnoFlashBanner.useEffect\"], [\n        position\n    ]);\n    const fetchBanner = async ()=>{\n        try {\n            setLoading(true);\n            // تحديد الموضع المناسب\n            let dbPosition = '';\n            switch(position){\n                case 'header':\n                    dbPosition = 'header';\n                    break;\n                case 'footer':\n                    dbPosition = 'footer';\n                    break;\n                case 'content':\n                    dbPosition = 'article-body-mid';\n                    break;\n                default:\n                    dbPosition = 'header';\n            }\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('advertisements').select('*').eq('position', dbPosition).eq('is_active', true).eq('is_paused', false).order('priority', {\n                ascending: true\n            }).limit(1);\n            if (error) {\n                console.error('Error fetching TechnoFlash banner:', error);\n                return;\n            }\n            // التحقق من وجود بيانات\n            if (data && data.length > 0) {\n                setBannerData(data[0]);\n            } else {\n                console.log('No TechnoFlash banner found for position:', dbPosition);\n                // لا نعرض إعلان افتراضي - نترك الإعلان فارغ\n                setBannerData(null);\n                return;\n            }\n            // تسجيل مشاهدة\n            if (data && data.length > 0) {\n                try {\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('advertisements').update({\n                        view_count: (data[0].view_count || 0) + 1,\n                        updated_at: new Date().toISOString()\n                    }).eq('id', data[0].id);\n                } catch (viewError) {\n                    console.error('Error updating view count:', viewError);\n                // لا نوقف العملية بسبب خطأ في تحديث العداد\n                }\n            }\n        } catch (error) {\n            console.error('Error in fetchBanner:', error);\n            setBannerData(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // تم حذف الإعلانات الافتراضية نهائياً\n    const getDefaultBannerData = (position)=>{\n        return null; // لا نعرض أي إعلانات افتراضية\n    };\n    const handleClick = async ()=>{\n        if (!bannerData) return;\n        try {\n            // تسجيل نقرة فقط للإعلانات الحقيقية (ليس الافتراضية)\n            if (!bannerData.id.startsWith('default-')) {\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_3__.supabase.from('advertisements').update({\n                    click_count: (bannerData.click_count || 0) + 1,\n                    updated_at: new Date().toISOString()\n                }).eq('id', bannerData.id);\n            }\n            // يمكن إضافة رابط هنا\n            console.log('TechnoFlash banner clicked!', bannerData.id);\n        } catch (error) {\n            console.error('Error tracking click:', error);\n        }\n    };\n    // عرض فارغ أثناء التحميل\n    if (loading || !bannerData) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HydrationSafeWrapper__WEBPACK_IMPORTED_MODULE_4__.ClientOnlyContent, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"jsx-2d3aa99984dc75c3\" + \" \" + \"techno-flash-banner-container \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"2d3aa99984dc75c3\",\n                    children: '@-webkit-keyframes slide-left{0%{-webkit-transform:translatex(100%);transform:translatex(100%)}100%{-webkit-transform:translatex(-100%);transform:translatex(-100%)}}@-moz-keyframes slide-left{0%{-moz-transform:translatex(100%);transform:translatex(100%)}100%{-moz-transform:translatex(-100%);transform:translatex(-100%)}}@-o-keyframes slide-left{0%{-o-transform:translatex(100%);transform:translatex(100%)}100%{-o-transform:translatex(-100%);transform:translatex(-100%)}}@keyframes slide-left{0%{-webkit-transform:translatex(100%);-moz-transform:translatex(100%);-o-transform:translatex(100%);transform:translatex(100%)}100%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%)}}@-webkit-keyframes glow{0%,100%{text-shadow:0 0 10px rgba(255,215,0,.5)}50%{text-shadow:0 0 20px rgba(255,215,0,.8),0 0 30px rgba(255,215,0,.6)}}@-moz-keyframes glow{0%,100%{text-shadow:0 0 10px rgba(255,215,0,.5)}50%{text-shadow:0 0 20px rgba(255,215,0,.8),0 0 30px rgba(255,215,0,.6)}}@-o-keyframes glow{0%,100%{text-shadow:0 0 10px rgba(255,215,0,.5)}50%{text-shadow:0 0 20px rgba(255,215,0,.8),0 0 30px rgba(255,215,0,.6)}}@keyframes glow{0%,100%{text-shadow:0 0 10px rgba(255,215,0,.5)}50%{text-shadow:0 0 20px rgba(255,215,0,.8),0 0 30px rgba(255,215,0,.6)}}.techno-flash-banner.jsx-2d3aa99984dc75c3{width:100%;background:-webkit-linear-gradient(45deg,#111,#222,#111);background:-moz-linear-gradient(45deg,#111,#222,#111);background:-o-linear-gradient(45deg,#111,#222,#111);background:linear-gradient(45deg,#111,#222,#111);-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:gradientShift 3s ease infinite;-moz-animation:gradientShift 3s ease infinite;-o-animation:gradientShift 3s ease infinite;animation:gradientShift 3s ease infinite;color:#fff;padding:12px 0;overflow:hidden;position:relative;cursor:pointer;-webkit-transition:all.3s ease;-moz-transition:all.3s ease;-o-transition:all.3s ease;transition:all.3s ease}.techno-flash-banner.jsx-2d3aa99984dc75c3:hover{background:-webkit-linear-gradient(45deg,#222,#333,#222);background:-moz-linear-gradient(45deg,#222,#333,#222);background:-o-linear-gradient(45deg,#222,#333,#222);background:linear-gradient(45deg,#222,#333,#222);-webkit-transform:scale(1.02);-moz-transform:scale(1.02);-ms-transform:scale(1.02);-o-transform:scale(1.02);transform:scale(1.02)}.techno-flash-banner.header.jsx-2d3aa99984dc75c3{border-bottom:3px solid#FFD700;-webkit-box-shadow:0 3px 15px rgba(255,215,0,.4);-moz-box-shadow:0 3px 15px rgba(255,215,0,.4);box-shadow:0 3px 15px rgba(255,215,0,.4)}.techno-flash-banner.footer.jsx-2d3aa99984dc75c3{border-top:3px solid#FFD700;-webkit-box-shadow:0 -3px 15px rgba(255,215,0,.4);-moz-box-shadow:0 -3px 15px rgba(255,215,0,.4);box-shadow:0 -3px 15px rgba(255,215,0,.4)}.techno-flash-banner.content.jsx-2d3aa99984dc75c3{border:3px solid#FFD700;-webkit-border-radius:15px;-moz-border-radius:15px;border-radius:15px;margin:20px 0;-webkit-box-shadow:0 0 20px rgba(255,215,0,.5);-moz-box-shadow:0 0 20px rgba(255,215,0,.5);box-shadow:0 0 20px rgba(255,215,0,.5)}.sliding-text.jsx-2d3aa99984dc75c3{display:inline-block;white-space:nowrap;-webkit-animation:slide-left 15s linear infinite,glow 2s ease-in-out infinite;-moz-animation:slide-left 15s linear infinite,glow 2s ease-in-out infinite;-o-animation:slide-left 15s linear infinite,glow 2s ease-in-out infinite;animation:slide-left 15s linear infinite,glow 2s ease-in-out infinite;font-size:20px;font-weight:bold;color:#FFD700;font-family:\"Arial\",sans-serif}.techno-flash-banner.jsx-2d3aa99984dc75c3:hover .sliding-text.jsx-2d3aa99984dc75c3{-webkit-animation-play-state:paused;-moz-animation-play-state:paused;-o-animation-play-state:paused;animation-play-state:paused;color:#FFF700}@-webkit-keyframes gradientShift{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-moz-keyframes gradientShift{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-o-keyframes gradientShift{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@keyframes gradientShift{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@media(max-width:768px){.sliding-text.jsx-2d3aa99984dc75c3{font-size:18px;-webkit-animation-duration:12s;-moz-animation-duration:12s;-o-animation-duration:12s;animation-duration:12s}.techno-flash-banner.jsx-2d3aa99984dc75c3{padding:10px 0}}@media(max-width:480px){.sliding-text.jsx-2d3aa99984dc75c3{font-size:16px;-webkit-animation-duration:10s;-moz-animation-duration:10s;-o-animation-duration:10s;animation-duration:10s}.techno-flash-banner.jsx-2d3aa99984dc75c3{padding:8px 0}}.techno-flash-banner.jsx-2d3aa99984dc75c3::before{content:\"\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:-webkit-linear-gradient(left,transparent,rgba(255,255,255,.1),transparent);background:-moz-linear-gradient(left,transparent,rgba(255,255,255,.1),transparent);background:-o-linear-gradient(left,transparent,rgba(255,255,255,.1),transparent);background:linear-gradient(90deg,transparent,rgba(255,255,255,.1),transparent);-webkit-animation:shine 3s infinite;-moz-animation:shine 3s infinite;-o-animation:shine 3s infinite;animation:shine 3s infinite}@-webkit-keyframes shine{0%{left:-100%}100%{left:100%}}@-moz-keyframes shine{0%{left:-100%}100%{left:100%}}@-o-keyframes shine{0%{left:-100%}100%{left:100%}}@keyframes shine{0%{left:-100%}100%{left:100%}}'\n                }, void 0, false, void 0, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    onClick: handleClick,\n                    dangerouslySetInnerHTML: {\n                        __html: bannerData.content\n                    },\n                    className: \"jsx-2d3aa99984dc75c3\" + \" \" + \"techno-flash-banner \".concat(position)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\TechnoFlashBanner.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, this),\n                bannerData.custom_js && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        __html: bannerData.custom_js\n                    },\n                    className: \"jsx-2d3aa99984dc75c3\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\TechnoFlashBanner.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\TechnoFlashBanner.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\TechnoFlashBanner.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(TechnoFlashBanner, \"CbQqHB8YMrDwAwngCuWzJAh1oo8=\");\n_c = TechnoFlashBanner;\n/**\n * مكونات جاهزة للاستخدام في مواضع مختلفة\n */ // إعلان الهيدر\nfunction TechnoFlashHeaderBanner(param) {\n    let { className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechnoFlashBanner, {\n        position: \"header\",\n        className: \"header-banner \".concat(className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\TechnoFlashBanner.tsx\",\n        lineNumber: 261,\n        columnNumber: 5\n    }, this);\n}\n_c1 = TechnoFlashHeaderBanner;\n// إعلان الفوتر\nfunction TechnoFlashFooterBanner(param) {\n    let { className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechnoFlashBanner, {\n        position: \"footer\",\n        className: \"footer-banner \".concat(className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\TechnoFlashBanner.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n_c2 = TechnoFlashFooterBanner;\n// إعلان المحتوى\nfunction TechnoFlashContentBanner(param) {\n    let { className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TechnoFlashBanner, {\n        position: \"content\",\n        className: \"content-banner \".concat(className)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\TechnoFlashBanner.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, this);\n} // تم حذف مكون الإعلان الثابت نهائياً - لن تظهر أي إعلانات ثابتة\n_c3 = TechnoFlashContentBanner;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"TechnoFlashBanner\");\n$RefreshReg$(_c1, \"TechnoFlashHeaderBanner\");\n$RefreshReg$(_c2, \"TechnoFlashFooterBanner\");\n$RefreshReg$(_c3, \"TechnoFlashContentBanner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ads/TechnoFlashBanner.tsx\n"));

/***/ })

});