"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-tools/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ads/SmartAdManager.tsx":
/*!***********************************************!*\
  !*** ./src/components/ads/SmartAdManager.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SmartAIToolAd: () => (/* binding */ SmartAIToolAd),\n/* harmony export */   SmartArticleAd: () => (/* binding */ SmartArticleAd),\n/* harmony export */   SmartContentAd: () => (/* binding */ SmartContentAd),\n/* harmony export */   SmartFooterAd: () => (/* binding */ SmartFooterAd),\n/* harmony export */   SmartHeaderAd: () => (/* binding */ SmartHeaderAd),\n/* harmony export */   SmartSharedAd: () => (/* binding */ SmartSharedAd),\n/* harmony export */   \"default\": () => (/* binding */ SmartAdManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _AdItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AdItem */ \"(app-pages-browser)/./src/components/ads/AdItem.tsx\");\n/* harmony import */ var _components_HydrationSafeWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/HydrationSafeWrapper */ \"(app-pages-browser)/./src/components/HydrationSafeWrapper.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,SmartArticleAd,SmartAIToolAd,SmartSharedAd,SmartHeaderAd,SmartContentAd,SmartFooterAd auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n/**\n * مدير إعلانات ذكي يربط بين المقالات وأدوات الذكاء الاصطناعي\n * يعرض إعلانات مشتركة ومتعلقة بالمحتوى\n */ function SmartAdManager(param) {\n    let { contentType, position, className = '', maxAds = 1, keywords = [], fallbackAdSenseSlot, showFallback = false, toolSlug } = param;\n    _s();\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartAdManager.useEffect\": ()=>{\n            fetchSmartAds();\n        }\n    }[\"SmartAdManager.useEffect\"], [\n        contentType,\n        position,\n        keywords,\n        toolSlug\n    ]);\n    const fetchSmartAds = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            let finalAds = [];\n            // إذا كان contentType هو ai-tool وتم تمرير toolSlug\n            if (contentType === 'ai-tool' && toolSlug) {\n                // أولاً: البحث عن إعلانات مخصصة لهذه الأداة\n                const { data: customAds } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('advertisements').select('*').eq('position', position).eq('target_ai_tool_slug', toolSlug).eq('is_active', true).eq('is_paused', false).order('priority', {\n                    ascending: true\n                }).limit(maxAds);\n                if (customAds && customAds.length > 0) {\n                    finalAds = customAds;\n                } else {\n                    // ثانياً: البحث عن إعلانات عامة لجميع الأدوات\n                    const { data: allToolsAds } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('advertisements').select('*').eq('position', position).eq('target_all_ai_tools', true).eq('is_active', true).eq('is_paused', false).order('priority', {\n                        ascending: true\n                    }).limit(maxAds);\n                    if (allToolsAds && allToolsAds.length > 0) {\n                        finalAds = allToolsAds;\n                    }\n                }\n            }\n            // إذا لم نجد إعلانات مخصصة، ابحث عن إعلانات عامة\n            if (finalAds.length === 0) {\n                let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('advertisements').select('*').eq('position', position).eq('is_active', true).eq('is_paused', false).is('target_ai_tool_slug', null).eq('target_all_ai_tools', false);\n                // فلترة حسب الكلمات المفتاحية إذا كانت متوفرة\n                if (keywords.length > 0) {\n                    const keywordFilter = keywords.map((keyword)=>\"title.ilike.%\".concat(keyword, \"%,content.ilike.%\").concat(keyword, \"%\")).join(',');\n                    query = query.or(keywordFilter);\n                }\n                // ترتيب حسب الأولوية والتاريخ\n                query = query.order('priority', {\n                    ascending: true\n                }).order('created_at', {\n                    ascending: false\n                }).limit(maxAds * 2);\n                const { data, error } = await query;\n                if (error) {\n                    console.error('Error fetching general ads:', error);\n                    setError('فشل في تحميل الإعلانات');\n                    return;\n                }\n                finalAds = data || [];\n            }\n            if (!finalAds || finalAds.length === 0) {\n                setError('لا توجد إعلانات متاحة');\n                return;\n            }\n            // فلترة الإعلانات الذكية\n            const filteredAds = filterSmartAds(finalAds);\n            setAds(filteredAds.slice(0, maxAds));\n        } catch (error) {\n            console.error('Error in fetchSmartAds:', error);\n            setError('حدث خطأ في تحميل الإعلانات');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterSmartAds = (allAds)=>{\n        // تصفية ذكية للإعلانات حسب نوع المحتوى\n        return allAds.filter((ad)=>{\n            // فحص التواريخ (إذا كانت متوفرة)\n            const now = new Date();\n            if (ad.start_date && new Date(ad.start_date) > now) return false;\n            if (ad.end_date && new Date(ad.end_date) < now) return false;\n            // فحص الكلمات المفتاحية\n            if (keywords.length > 0) {\n                const adText = \"\".concat(ad.title, \" \").concat(ad.content).toLowerCase();\n                const hasKeyword = keywords.some((keyword)=>adText.includes(keyword.toLowerCase()));\n                if (hasKeyword) return true;\n            }\n            // إعلانات مشتركة (تظهر في كلا النوعين)\n            const sharedKeywords = [\n                'ذكاء اصطناعي',\n                'ai',\n                'تقنية',\n                'برمجة',\n                'تطوير'\n            ];\n            const adText = \"\".concat(ad.title, \" \").concat(ad.content).toLowerCase();\n            const isShared = sharedKeywords.some((keyword)=>adText.includes(keyword));\n            if (isShared) return true;\n            // إعلانات خاصة بالمقالات\n            if (contentType === 'article') {\n                const articleKeywords = [\n                    'مقال',\n                    'قراءة',\n                    'تعلم',\n                    'دورة'\n                ];\n                return articleKeywords.some((keyword)=>adText.includes(keyword));\n            }\n            // إعلانات خاصة بأدوات الذكاء الاصطناعي\n            if (contentType === 'ai-tool') {\n                const toolKeywords = [\n                    'أداة',\n                    'tool',\n                    'premium',\n                    'مميز'\n                ];\n                return toolKeywords.some((keyword)=>adText.includes(keyword));\n            }\n            return true;\n        });\n    };\n    // عرض حالة التحميل\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"smart-ad-loading \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-lg animate-pulse p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-700 rounded w-3/4 mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-gray-700 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    // عرض الخطأ مع fallback\n    if (error && showFallback && fallbackAdSenseSlot) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HydrationSafeWrapper__WEBPACK_IMPORTED_MODULE_4__.ClientOnlyContent, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"smart-ad-fallback \".concat(className),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        async: true,\n                        src: \"https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n                        className: \"adsbygoogle\",\n                        style: {\n                            display: 'block'\n                        },\n                        \"data-ad-client\": \"ca-pub-1234567890123456\",\n                        \"data-ad-slot\": fallbackAdSenseSlot,\n                        \"data-ad-format\": \"auto\",\n                        \"data-full-width-responsive\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        children: \"(adsbygoogle = window.adsbygoogle || []).push({});\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this);\n    }\n    // عرض الإعلانات - بدون مساحة إضافية إذا لم توجد إعلانات\n    if (ads.length === 0) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HydrationSafeWrapper__WEBPACK_IMPORTED_MODULE_4__.ClientOnlyContent, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"smart-ad-container \".concat(className, \" \").concat(ads.length > 0 ? 'mb-6 md:mb-8' : ''),\n            children: ads.map((ad, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"smart-ad-item \".concat(index > 0 ? 'mt-4' : ''),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdItem__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            ad: ad,\n                            className: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 13\n                        }, this),\n                         true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500 text-center mt-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-blue-900 text-blue-300 px-2 py-1 rounded\",\n                                children: [\n                                    \"إعلان ذكي - \",\n                                    contentType\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, ad.id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n        lineNumber: 238,\n        columnNumber: 5\n    }, this);\n}\n_s(SmartAdManager, \"VFlxUKEDrDJnKtFlCvwG1YNgxVM=\");\n_c = SmartAdManager;\n/**\n * مكونات إعلانات ذكية جاهزة للاستخدام\n */ // إعلان ذكي للمقالات\nfunction SmartArticleAd(param) {\n    let { position, className = '', keywords = [] } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmartAdManager, {\n        contentType: \"article\",\n        position: position,\n        className: className,\n        keywords: [\n            ...keywords,\n            'مقال',\n            'قراءة',\n            'تعلم'\n        ],\n        fallbackAdSenseSlot: \"1234567890\",\n        showFallback: true\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n        lineNumber: 274,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SmartArticleAd;\n// إعلان ذكي لأدوات الذكاء الاصطناعي\nfunction SmartAIToolAd(param) {\n    let { position, className = '', keywords = [], toolSlug } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmartAdManager, {\n        contentType: \"ai-tool\",\n        position: position,\n        className: className,\n        keywords: [\n            ...keywords,\n            'أداة',\n            'ذكاء اصطناعي',\n            'AI'\n        ],\n        fallbackAdSenseSlot: \"1234567891\",\n        showFallback: true,\n        toolSlug: toolSlug\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, this);\n}\n_c2 = SmartAIToolAd;\n// إعلان مشترك (يظهر في كلا النوعين)\nfunction SmartSharedAd(param) {\n    let { position, className = '', keywords = [] } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmartAdManager, {\n        contentType: \"both\",\n        position: position,\n        className: className,\n        keywords: [\n            ...keywords,\n            'تقنية',\n            'برمجة',\n            'تطوير'\n        ],\n        fallbackAdSenseSlot: \"1234567892\",\n        showFallback: true\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n        lineNumber: 321,\n        columnNumber: 5\n    }, this);\n}\n_c3 = SmartSharedAd;\n// إعلان ذكي للهيدر (مشترك)\nfunction SmartHeaderAd(param) {\n    let { className = 'mb-6' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmartSharedAd, {\n        position: \"header\",\n        className: className,\n        keywords: [\n            'دورة',\n            'تعلم',\n            'ذكاء اصطناعي'\n        ]\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n        lineNumber: 335,\n        columnNumber: 5\n    }, this);\n}\n_c4 = SmartHeaderAd;\n// إعلان ذكي للمحتوى (متكيف)\nfunction SmartContentAd(param) {\n    let { contentType, className = 'my-8', keywords = [] } = param;\n    if (contentType === 'article') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmartArticleAd, {\n            position: \"article-body-mid\",\n            className: className,\n            keywords: keywords\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n            lineNumber: 355,\n            columnNumber: 7\n        }, this);\n    } else {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmartAIToolAd, {\n            position: \"in-content\",\n            className: className,\n            keywords: keywords\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n            lineNumber: 363,\n            columnNumber: 7\n        }, this);\n    }\n}\n_c5 = SmartContentAd;\n// إعلان ذكي للفوتر (مشترك)\nfunction SmartFooterAd(param) {\n    let { className = 'mt-8' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SmartSharedAd, {\n        position: \"footer\",\n        className: className,\n        keywords: [\n            'مجتمع',\n            'انضم',\n            'تواصل'\n        ]\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SmartAdManager.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, this);\n}\n_c6 = SmartFooterAd;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"SmartAdManager\");\n$RefreshReg$(_c1, \"SmartArticleAd\");\n$RefreshReg$(_c2, \"SmartAIToolAd\");\n$RefreshReg$(_c3, \"SmartSharedAd\");\n$RefreshReg$(_c4, \"SmartHeaderAd\");\n$RefreshReg$(_c5, \"SmartContentAd\");\n$RefreshReg$(_c6, \"SmartFooterAd\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ads/SmartAdManager.tsx\n"));

/***/ })

});