"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-tools/page",{

/***/ "(app-pages-browser)/./src/components/ai-tools/LazyAIToolsGrid.tsx":
/*!*****************************************************!*\
  !*** ./src/components/ai-tools/LazyAIToolsGrid.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LazyAIToolsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n/**\n * مكون تحميل تدريجي لأدوات الذكاء الاصطناعي\n * يحمل البيانات على دفعات لتحسين الأداء\n */ function LazyAIToolsGrid(param) {\n    let { initialTools = [], pageSize = 12, category = '', searchQuery = '' } = param;\n    _s();\n    const [tools, setTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTools);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // تحميل المزيد من الأدوات\n    const loadMoreTools = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LazyAIToolsGrid.useCallback[loadMoreTools]\": async ()=>{\n            if (loading || !hasMore) return;\n            try {\n                setLoading(true);\n                setError(null);\n                let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('ai_tools').select('*').range(page * pageSize, (page + 1) * pageSize - 1).order('created_at', {\n                    ascending: false\n                });\n                // فلترة حسب الفئة\n                if (category && category !== 'all') {\n                    query = query.eq('category', category);\n                }\n                // فلترة حسب البحث\n                if (searchQuery) {\n                    query = query.or(\"name.ilike.%\".concat(searchQuery, \"%,description.ilike.%\").concat(searchQuery, \"%\"));\n                }\n                const { data, error } = await query;\n                if (error) {\n                    console.error('Error loading tools:', error);\n                    setError('خطأ في تحميل الأدوات');\n                    return;\n                }\n                if (data && data.length > 0) {\n                    setTools({\n                        \"LazyAIToolsGrid.useCallback[loadMoreTools]\": (prev)=>page === 0 ? data : [\n                                ...prev,\n                                ...data\n                            ]\n                    }[\"LazyAIToolsGrid.useCallback[loadMoreTools]\"]);\n                    setPage({\n                        \"LazyAIToolsGrid.useCallback[loadMoreTools]\": (prev)=>prev + 1\n                    }[\"LazyAIToolsGrid.useCallback[loadMoreTools]\"]);\n                    // إذا كان عدد النتائج أقل من pageSize، فلا توجد صفحات أخرى\n                    if (data.length < pageSize) {\n                        setHasMore(false);\n                    }\n                } else {\n                    setHasMore(false);\n                }\n            } catch (error) {\n                console.error('Error in loadMoreTools:', error);\n                setError('خطأ في تحميل الأدوات');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"LazyAIToolsGrid.useCallback[loadMoreTools]\"], [\n        page,\n        pageSize,\n        category,\n        searchQuery,\n        loading,\n        hasMore\n    ]);\n    // إعادة تعيين عند تغيير الفلاتر\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LazyAIToolsGrid.useEffect\": ()=>{\n            setTools([]);\n            setPage(0);\n            setHasMore(true);\n            setError(null);\n        }\n    }[\"LazyAIToolsGrid.useEffect\"], [\n        category,\n        searchQuery\n    ]);\n    // تحميل الصفحة الأولى عند تغيير الفلاتر\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LazyAIToolsGrid.useEffect\": ()=>{\n            if (tools.length === 0 && hasMore) {\n                loadMoreTools();\n            }\n        }\n    }[\"LazyAIToolsGrid.useEffect\"], [\n        tools.length,\n        hasMore,\n        loadMoreTools\n    ]);\n    const getPricingColor = (pricing)=>{\n        switch(pricing){\n            case 'free':\n                return 'border-green-500 text-green-400 bg-green-500/10';\n            case 'freemium':\n                return 'border-yellow-500 text-yellow-400 bg-yellow-500/10';\n            case 'paid':\n                return 'border-red-500 text-red-400 bg-red-500/10';\n            default:\n                return 'border-gray-500 text-gray-400 bg-gray-500/10';\n        }\n    };\n    const getPricingText = (pricing)=>{\n        switch(pricing){\n            case 'free':\n                return 'مجاني';\n            case 'freemium':\n                return 'مجاني جزئياً';\n            case 'paid':\n                return 'مدفوع';\n            default:\n                return 'غير محدد';\n        }\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-400 mb-4\",\n                    children: [\n                        \"❌ \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setError(null);\n                        loadMoreTools();\n                    },\n                    className: \"bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                    children: \"إعادة المحاولة\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-dark-card rounded-xl overflow-hidden border border-gray-800 hover:border-primary/50 transition-all duration-300 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-48 bg-gradient-to-br from-primary/10 to-blue-600/10\",\n                                children: tool.logo_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: tool.logo_url,\n                                    alt: tool.name,\n                                    fill: true,\n                                    style: {\n                                        objectFit: \"contain\"\n                                    },\n                                    className: \"p-4 group-hover:scale-105 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-primary/20 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-primary\",\n                                            children: tool.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white group-hover:text-primary transition-colors duration-300 line-clamp-1\",\n                                                children: tool.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 rounded-full text-xs font-medium border \".concat(getPricingColor(tool.pricing)),\n                                                children: getPricingText(tool.pricing)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-dark-text-secondary mb-4 line-clamp-2\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-primary/20 text-primary px-2 py-1 rounded text-sm\",\n                                                children: tool.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            tool.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 mr-1\",\n                                                        children: \"⭐\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-300\",\n                                                        children: tool.rating\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    tool.features && tool.features.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: [\n                                                tool.features.slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-gray-800 text-gray-300 px-2 py-1 rounded text-xs\",\n                                                        children: feature\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 23\n                                                    }, this)),\n                                                tool.features.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 text-xs\",\n                                                    children: [\n                                                        \"+\",\n                                                        tool.features.length - 3,\n                                                        \" المزيد\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: \"/ai-tools/\".concat(tool.slug),\n                                                className: \"flex-1 bg-primary text-white text-center py-2 px-4 rounded-lg hover:bg-primary/90 transition-colors duration-300 text-sm font-medium\",\n                                                children: \"عرض التفاصيل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            tool.website_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: tool.website_url,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"bg-gray-700 text-white py-2 px-3 rounded-lg hover:bg-gray-600 transition-colors duration-300 text-sm\",\n                                                children: \"\\uD83D\\uDD17\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, tool.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: loadMoreTools,\n                    disabled: loading,\n                    className: \"bg-primary text-white px-8 py-3 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300 font-medium\",\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 17\n                            }, this),\n                            \"جاري التحميل...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 15\n                    }, this) : 'تحميل المزيد'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                lineNumber: 251,\n                columnNumber: 9\n            }, this),\n            !loading && tools.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 mb-4\",\n                        children: \"\\uD83D\\uDD0D لا توجد أدوات متاحة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"جرب تغيير معايير البحث أو الفلترة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                lineNumber: 271,\n                columnNumber: 9\n            }, this),\n            loading && tools.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: Array.from({\n                    length: pageSize\n                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-dark-card rounded-xl overflow-hidden border border-gray-800 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-48 bg-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-700 rounded mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-700 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-700 rounded mb-4 w-3/4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-gray-700 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\9999999\\\\techno-flashi\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(LazyAIToolsGrid, \"iY0f6Plr+RrkJ/DhNnU8aITaklE=\");\n_c = LazyAIToolsGrid;\nvar _c;\n$RefreshReg$(_c, \"LazyAIToolsGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-tools/LazyAIToolsGrid.tsx\n"));

/***/ })

});